/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/target/debug/deps/tt_quant_core-c1934595ed57289e.d: /Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/clippy.toml native/tt_quant_core/src/lib.rs Cargo.toml

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/target/debug/deps/libtt_quant_core-c1934595ed57289e.rmeta: /Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/clippy.toml native/tt_quant_core/src/lib.rs Cargo.toml

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/clippy.toml:
native/tt_quant_core/src/lib.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=-D__CLIPPY_HACKERY__warnings__CLIPPY_HACKERY__
# env-dep:CLIPPY_CONF_DIR
