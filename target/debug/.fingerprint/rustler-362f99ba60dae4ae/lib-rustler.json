{"rustc": 17575471286409424799, "features": "[\"default\", \"nif_version_2_14\", \"nif_version_2_15\"]", "declared_features": "[\"allocator\", \"alternative_nif_init_name\", \"big_integer\", \"default\", \"derive\", \"nif_version_2_14\", \"nif_version_2_15\", \"nif_version_2_16\", \"nif_version_2_17\", \"serde\"]", "target": 14078336547572991944, "profile": 8276155916380437441, "path": 9010510377826870944, "deps": [[6684468221781381339, "build_script_build", false, 8167561989108654817], [11192626958996522601, "inventory", false, 10588977254162458897], [13587469111750606423, "libloading", false, 1829460436928644835], [14867639939897172561, "rustler_codegen", false, 3134590564081715973]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustler-362f99ba60dae4ae/dep-lib-rustler", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}