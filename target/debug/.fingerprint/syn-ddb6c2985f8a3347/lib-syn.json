{"rustc": 17575471286409424799, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 12241837977478637838, "deps": [[373107762698212489, "proc_macro2", false, 6139631345617686079], [1988483478007900009, "unicode_ident", false, 7501407442827053675], [17990358020177143287, "quote", false, 13434595867336982140]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-ddb6c2985f8a3347/dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}