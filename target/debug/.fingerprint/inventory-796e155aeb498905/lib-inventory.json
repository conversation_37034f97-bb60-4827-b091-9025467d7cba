{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 4358031002414937057, "profile": 3033921117576893, "path": 339185425148430946, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/inventory-796e155aeb498905/dep-lib-inventory", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}