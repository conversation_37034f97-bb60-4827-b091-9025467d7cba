{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[\"core\", \"rustc-dep-of-std\"]", "target": 13840298032947503755, "profile": 8276155916380437441, "path": 8894811266776312635, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cfg-if-82f5e27af9879f31/dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}