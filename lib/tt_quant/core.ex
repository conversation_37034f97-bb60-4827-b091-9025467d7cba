defmodule TtQuant.Core do
  @moduledoc """
  NIF module for TtQuant core functionality implemented in Rust.
  
  This module provides high-performance computational functions
  for quantitative trading operations.
  """
  
  use <PERSON><PERSON><PERSON>, otp_app: :tt_quant, crate: "ttquant_core"

  @doc """
  Adds two integers together.
  
  This is a sample NIF function to verify the Rust integration is working.
  
  ## Examples
  
      iex> TtQuant.Core.add(1, 2)
      3
      
      iex> TtQuant.Core.add(-5, 10)
      5
  """
  # When your NIF is loaded, it will override this function.
  def add(_a, _b), do: :erlang.nif_error(:nif_not_loaded)
end
