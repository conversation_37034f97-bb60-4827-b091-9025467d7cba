defmodule TtQuant.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      TtQuantWeb.Telemetry,
      TtQuant.Repo,
      {DNSCluster, query: Application.get_env(:tt_quant, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: TtQuant.PubSub},
      # Start a worker by calling: TtQuant.Worker.start_link(arg)
      # {TtQuant.Worker, arg},
      # Start to serve requests, typically the last entry
      TtQuantWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: TtQuant.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    TtQuantWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
