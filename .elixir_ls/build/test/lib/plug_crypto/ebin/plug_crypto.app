{application,plug_crypto,
             [{modules,['Elixir.Plug.Crypto','Elixir.Plug.Crypto.Application',
                        'Elixir.Plug.Crypto.KeyGenerator',
                        'Elixir.Plug.Crypto.MessageEncryptor',
                        'Elixir.Plug.Crypto.MessageVerifier']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,crypto]},
              {description,"Crypto-related functionality for the web"},
              {registered,[]},
              {vsn,"2.1.1"},
              {mod,{'Elixir.Plug.Crypto.Application',[]}}]}.
