{application,phoenix_ecto,
             [{modules,['Elixir.Phoenix.Ecto',
                        'Elixir.Phoenix.Ecto.CheckRepoStatus',
                        'Elixir.Phoenix.Ecto.PendingMigrationError',
                        'Elixir.Phoenix.Ecto.SQL.Sandbox',
                        'Elixir.Phoenix.Ecto.SQL.SandboxSession',
                        'Elixir.Phoenix.Ecto.StorageNotCreatedError',
                        'Elixir.Phoenix.HTML.FormData.Ecto.Changeset',
                        'Elixir.Phoenix.HTML.Safe.Decimal',
                        'Elixir.Plug.Exception.Ecto.CastError',
                        'Elixir.Plug.Exception.Ecto.NoResultsError',
                        'Elixir.Plug.Exception.Ecto.Query.CastError',
                        'Elixir.Plug.Exception.Ecto.StaleEntryError',
                        'Elixir.Plug.Exception.Ecto.SubQueryError',
                        'Elixir.Plug.Exception.Phoenix.Ecto.PendingMigrationError',
                        'Elixir.Plug.Exception.Phoenix.Ecto.StorageNotCreatedError',
                        'Elixir.Plug.Exception.Postgrex.Error']},
              {optional_applications,[phoenix_html,postgrex]},
              {applications,[kernel,stdlib,elixir,logger,phoenix_html,ecto,
                             plug,postgrex]},
              {description,"Integration between Phoenix & Ecto"},
              {registered,[]},
              {vsn,"4.6.5"},
              {mod,{'Elixir.Phoenix.Ecto',[]}},
              {env,[{exclude_ecto_exceptions_from_plug,[]}]}]}.
