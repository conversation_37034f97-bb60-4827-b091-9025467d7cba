{application,msgpax,
             [{modules,['Elixir.Inspect.Msgpax.Bin',
                        'Elixir.Inspect.Msgpax.Ext',
                        'Elixir.Inspect.Msgpax.Fragment','Elixir.Msgpax',
                        'Elixir.Msgpax.Bin','Elixir.Msgpax.Ext',
                        'Elixir.Msgpax.Ext.Unpacker','Elixir.Msgpax.Fragment',
                        'Elixir.Msgpax.PackError','Elixir.Msgpax.Packer',
                        'Elixir.Msgpax.Packer.Any',
                        'Elixir.Msgpax.Packer.Atom',
                        'Elixir.Msgpax.Packer.BitString',
                        'Elixir.Msgpax.Packer.DateTime',
                        'Elixir.Msgpax.Packer.Float',
                        'Elixir.Msgpax.Packer.Integer',
                        'Elixir.Msgpax.Packer.List',
                        'Elixir.Msgpax.Packer.Map',
                        'Elixir.Msgpax.Packer.Msgpax.Bin',
                        'Elixir.Msgpax.Packer.Msgpax.Ext',
                        'Elixir.Msgpax.Packer.Msgpax.Fragment',
                        'Elixir.Msgpax.Packer.Msgpax.ReservedExt',
                        'Elixir.Msgpax.PlugParser',
                        'Elixir.Msgpax.ReservedExt',
                        'Elixir.Msgpax.UnpackError','Elixir.Msgpax.Unpacker']},
              {optional_applications,[plug]},
              {applications,[kernel,stdlib,elixir,plug]},
              {description,"A high-performance and comprehensive library for serializing and deserializing Elixir terms using the MessagePack format."},
              {registered,[]},
              {vsn,"2.4.0"}]}.
