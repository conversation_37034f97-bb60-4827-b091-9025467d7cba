{application,tt_quant,
             [{modules,['Elixir.TtQuant','Elixir.TtQuant.Application',
                        'Elixir.TtQuant.DataCase','Elixir.TtQuant.Mailer',
                        'Elixir.TtQuant.Repo','Elixir.TtQuantWeb',
                        'Elixir.TtQuantWeb.ConnCase',
                        'Elixir.TtQuantWeb.CoreComponents',
                        'Elixir.TtQuantWeb.Endpoint',
                        'Elixir.TtQuantWeb.ErrorHTML',
                        'Elixir.TtQuantWeb.ErrorJSON',
                        'Elixir.TtQuantWeb.Gettext',
                        'Elixir.TtQuantWeb.Layouts',
                        'Elixir.TtQuantWeb.PageController',
                        'Elixir.TtQuantWeb.PageHTML',
                        'Elixir.TtQuantWeb.Router',
                        'Elixir.TtQuantWeb.Telemetry']},
              {compile_env,[{tt_quant,['Elixir.TtQuantWeb.Endpoint',
                                       code_reloader],
                                      error},
                            {tt_quant,['Elixir.TtQuantWeb.Endpoint',
                                       debug_errors],
                                      error},
                            {tt_quant,['Elixir.TtQuantWeb.Endpoint',force_ssl],
                                      error},
                            {tt_quant,['Elixir.TtQuantWeb.Gettext'],error},
                            {tt_quant,[dev_routes],error}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,runtime_tools,
                             phoenix,phoenix_ecto,ecto_sql,postgrex,
                             phoenix_html,phoenix_live_view,lazy_html,
                             phoenix_live_dashboard,swoosh,req,
                             telemetry_metrics,telemetry_poller,gettext,jason,
                             dns_cluster,bandit]},
              {description,"tt_quant"},
              {registered,[]},
              {vsn,"0.1.0"},
              {mod,{'Elixir.TtQuant.Application',[]}}]}.
