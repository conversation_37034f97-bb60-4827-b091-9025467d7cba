/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/unicode_ident-565cb4904fd1ece8.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/tables.rs

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/libunicode_ident-565cb4904fd1ece8.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/tables.rs

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/libunicode_ident-565cb4904fd1ece8.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/tables.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.18/src/tables.rs:
