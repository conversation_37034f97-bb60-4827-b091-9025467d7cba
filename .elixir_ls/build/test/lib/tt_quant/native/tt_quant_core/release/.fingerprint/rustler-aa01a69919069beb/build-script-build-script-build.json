{"rustc": 17575471286409424799, "features": "[\"default\", \"nif_version_2_14\", \"nif_version_2_15\"]", "declared_features": "[\"allocator\", \"alternative_nif_init_name\", \"big_integer\", \"default\", \"derive\", \"nif_version_2_14\", \"nif_version_2_15\", \"nif_version_2_16\", \"nif_version_2_17\", \"serde\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 6558584779778925008, "deps": [[7372363573211779754, "regex_lite", false, 13759895603076201470]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustler-aa01a69919069beb/dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}