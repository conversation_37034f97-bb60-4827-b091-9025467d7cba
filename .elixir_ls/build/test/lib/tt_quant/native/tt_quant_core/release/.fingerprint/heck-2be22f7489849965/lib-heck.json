{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 17886154901722686619, "profile": 1369601567987815722, "path": 5219621967143037953, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/heck-2be22f7489849965/dep-lib-heck", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}