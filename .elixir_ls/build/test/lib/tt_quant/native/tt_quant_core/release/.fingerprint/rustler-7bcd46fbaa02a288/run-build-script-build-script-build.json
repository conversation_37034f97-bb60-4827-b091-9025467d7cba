{"rustc": 17575471286409424799, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6684468221781381339, "build_script_build", false, 12760201469710997447]], "local": [{"RerunIfChanged": {"output": "release/build/rustler-7bcd46fbaa02a288/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 0, "compile_kind": 0}