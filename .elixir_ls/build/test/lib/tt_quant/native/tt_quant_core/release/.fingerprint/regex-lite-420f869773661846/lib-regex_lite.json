{"rustc": 17575471286409424799, "features": "[\"default\", \"std\", \"string\"]", "declared_features": "[\"default\", \"std\", \"string\"]", "target": 300499141083605431, "profile": 1369601567987815722, "path": 11898401402810945180, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-lite-420f869773661846/dep-lib-regex_lite", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}