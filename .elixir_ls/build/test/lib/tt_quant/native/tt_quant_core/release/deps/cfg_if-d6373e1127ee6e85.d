/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/cfg_if-d6373e1127ee6e85.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.3/src/lib.rs

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/libcfg_if-d6373e1127ee6e85.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.3/src/lib.rs

/Users/<USER>/Code/github.com/nautechsystems/nautilus_trader/tt_quant/.elixir_ls/build/test/lib/tt_quant/native/tt_quant_core/release/deps/libcfg_if-d6373e1127ee6e85.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.3/src/lib.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.3/src/lib.rs:
