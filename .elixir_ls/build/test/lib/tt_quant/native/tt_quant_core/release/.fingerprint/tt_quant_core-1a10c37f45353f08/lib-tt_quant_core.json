{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 14671404803125106998, "profile": 2040997289075261528, "path": 17114978713780351013, "deps": [[6684468221781381339, "rustler", false, 16161036962511711991]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tt_quant_core-1a10c37f45353f08/dep-lib-tt_quant_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}