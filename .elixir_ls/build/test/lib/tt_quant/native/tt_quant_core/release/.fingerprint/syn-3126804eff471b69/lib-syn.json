{"rustc": 17575471286409424799, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 12241837977478637838, "deps": [[373107762698212489, "proc_macro2", false, 14364893793826041086], [1988483478007900009, "unicode_ident", false, 3956406041420023624], [17990358020177143287, "quote", false, 11194258521300643972]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-3126804eff471b69/dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}