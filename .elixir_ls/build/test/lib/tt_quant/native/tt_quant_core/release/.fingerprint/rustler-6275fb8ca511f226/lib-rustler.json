{"rustc": 17575471286409424799, "features": "[\"default\", \"nif_version_2_14\", \"nif_version_2_15\"]", "declared_features": "[\"allocator\", \"alternative_nif_init_name\", \"big_integer\", \"default\", \"derive\", \"nif_version_2_14\", \"nif_version_2_15\", \"nif_version_2_16\", \"nif_version_2_17\", \"serde\"]", "target": 14078336547572991944, "profile": 2040997289075261528, "path": 9010510377826870944, "deps": [[6684468221781381339, "build_script_build", false, 1115578268116241291], [11192626958996522601, "inventory", false, 3933810071599120265], [13587469111750606423, "libloading", false, 12840565997832734592], [14867639939897172561, "rustler_codegen", false, 10421657942297695858]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustler-6275fb8ca511f226/dep-lib-rustler", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-Aclippy::drop_non_drop", "-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"], "config": 2069994364910194474, "compile_kind": 0}