{application,bandit,
             [{modules,['Elixir.Bandit','Elixir.Bandit.Adapter',
                        'Elixir.Bandit.Application','Elixir.Bandit.Clock',
                        'Elixir.Bandit.Compression',
                        'Elixir.Bandit.DelegatingHandler',
                        'Elixir.Bandit.Extractor',
                        'Elixir.Bandit.HTTP1.Handler',
                        'Elixir.Bandit.HTTP1.Socket',
                        'Elixir.Bandit.HTTP2.Connection',
                        'Elixir.Bandit.HTTP2.Errors',
                        'Elixir.Bandit.HTTP2.Errors.ConnectionError',
                        'Elixir.Bandit.HTTP2.Errors.StreamError',
                        'Elixir.Bandit.HTTP2.FlowControl',
                        'Elixir.Bandit.HTTP2.Frame',
                        'Elixir.Bandit.HTTP2.Frame.Continuation',
                        'Elixir.Bandit.HTTP2.Frame.Data',
                        'Elixir.Bandit.HTTP2.Frame.Flags',
                        'Elixir.Bandit.HTTP2.Frame.Goaway',
                        'Elixir.Bandit.HTTP2.Frame.Headers',
                        'Elixir.Bandit.HTTP2.Frame.Ping',
                        'Elixir.Bandit.HTTP2.Frame.Priority',
                        'Elixir.Bandit.HTTP2.Frame.PushPromise',
                        'Elixir.Bandit.HTTP2.Frame.RstStream',
                        'Elixir.Bandit.HTTP2.Frame.Serializable',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Continuation',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Data',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Goaway',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Headers',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Ping',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Priority',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.RstStream',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.Settings',
                        'Elixir.Bandit.HTTP2.Frame.Serializable.Bandit.HTTP2.Frame.WindowUpdate',
                        'Elixir.Bandit.HTTP2.Frame.Settings',
                        'Elixir.Bandit.HTTP2.Frame.Unknown',
                        'Elixir.Bandit.HTTP2.Frame.WindowUpdate',
                        'Elixir.Bandit.HTTP2.Handler',
                        'Elixir.Bandit.HTTP2.Settings',
                        'Elixir.Bandit.HTTP2.Stream',
                        'Elixir.Bandit.HTTP2.StreamCollection',
                        'Elixir.Bandit.HTTP2.StreamProcess',
                        'Elixir.Bandit.HTTPError',
                        'Elixir.Bandit.HTTPTransport',
                        'Elixir.Bandit.HTTPTransport.Bandit.HTTP1.Socket',
                        'Elixir.Bandit.HTTPTransport.Bandit.HTTP2.Stream',
                        'Elixir.Bandit.Headers',
                        'Elixir.Bandit.InitialHandler','Elixir.Bandit.Logger',
                        'Elixir.Bandit.PhoenixAdapter',
                        'Elixir.Bandit.Pipeline',
                        'Elixir.Bandit.PrimitiveOps.WebSocket',
                        'Elixir.Bandit.SocketHelpers',
                        'Elixir.Bandit.Telemetry','Elixir.Bandit.Trace',
                        'Elixir.Bandit.TransportError',
                        'Elixir.Bandit.WebSocket.Connection',
                        'Elixir.Bandit.WebSocket.Frame',
                        'Elixir.Bandit.WebSocket.Frame.Binary',
                        'Elixir.Bandit.WebSocket.Frame.ConnectionClose',
                        'Elixir.Bandit.WebSocket.Frame.Continuation',
                        'Elixir.Bandit.WebSocket.Frame.Ping',
                        'Elixir.Bandit.WebSocket.Frame.Pong',
                        'Elixir.Bandit.WebSocket.Frame.Serializable',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.Binary',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.ConnectionClose',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.Continuation',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.Ping',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.Pong',
                        'Elixir.Bandit.WebSocket.Frame.Serializable.Bandit.WebSocket.Frame.Text',
                        'Elixir.Bandit.WebSocket.Frame.Text',
                        'Elixir.Bandit.WebSocket.Handler',
                        'Elixir.Bandit.WebSocket.Handshake',
                        'Elixir.Bandit.WebSocket.PerMessageDeflate',
                        'Elixir.Bandit.WebSocket.Socket',
                        'Elixir.Bandit.WebSocket.Socket.ThousandIsland.Socket',
                        'Elixir.Bandit.WebSocket.UpgradeValidation']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,thousand_island,plug,
                             websock,hpax,telemetry]},
              {description,"A pure-Elixir HTTP server built for Plug & WebSock apps"},
              {registered,[]},
              {vsn,"1.8.0"},
              {mod,{'Elixir.Bandit.Application',[]}}]}.
