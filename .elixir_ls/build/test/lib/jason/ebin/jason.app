{application,jason,
             [{modules,['Elixir.Enumerable.Jason.OrderedObject',
                        'Elixir.Jason','Elixir.Jason.Codegen',
                        'Elixir.Jason.DecodeError','Elixir.Jason.Decoder',
                        'Elixir.Jason.Decoder.Unescape','Elixir.Jason.Encode',
                        'Elixir.Jason.EncodeError','Elixir.Jason.Encoder',
                        'Elixir.Jason.Encoder.Any',
                        'Elixir.Jason.Encoder.Atom',
                        'Elixir.Jason.Encoder.BitString',
                        'Elixir.Jason.Encoder.Date',
                        'Elixir.Jason.Encoder.DateTime',
                        'Elixir.Jason.Encoder.Decimal',
                        'Elixir.Jason.Encoder.Float',
                        'Elixir.Jason.Encoder.Integer',
                        'Elixir.Jason.Encoder.Jason.Fragment',
                        'Elixir.Jason.Encoder.Jason.OrderedObject',
                        'Elixir.Jason.Encoder.List',
                        'Elixir.<PERSON>.Encoder.Map',
                        'Elixir.Jason.Encoder.NaiveDateTime',
                        'Elixir.Jason.Encoder.Time','Elixir.Jason.Formatter',
                        'Elixir.Jason.Fragment','Elixir.Jason.Helpers',
                        'Elixir.Jason.OrderedObject','Elixir.Jason.Sigil']},
              {optional_applications,[decimal]},
              {applications,[kernel,stdlib,elixir,decimal]},
              {description,"A blazing fast JSON parser and generator in pure Elixir.\n"},
              {registered,[]},
              {vsn,"1.4.4"}]}.
