{application,phoenix_live_view,
    [{modules,
         ['Elixir.Enumerable.Phoenix.LiveView.LiveStream',
          'Elixir.Inspect.Phoenix.LiveView.Socket',
          'Elixir.Inspect.Phoenix.LiveView.Socket.AssignsNotInSocket',
          'Elixir.Inspect.Phoenix.LiveView.UploadConfig',
          'Elixir.Inspect.Phoenix.LiveViewTest.Element',
          'Elixir.Inspect.Phoenix.LiveViewTest.Upload',
          'Elixir.Inspect.Phoenix.LiveViewTest.View',
          'Elixir.Mix.Tasks.Compile.PhoenixLiveView',
          'Elixir.Mix.Tasks.PhoenixLiveView.Upgrade',
          'Elixir.Phoenix.Component','Elixir.Phoenix.Component.Declarative',
          'Elixir.Phoenix.Component.MacroComponent',
          'Elixir.Phoenix.HTML.Safe.Phoenix.LiveComponent.CID',
          'Elixir.Phoenix.HTML.Safe.Phoenix.LiveView.Component',
          'Elixir.Phoenix.HTML.Safe.Phoenix.LiveView.Comprehension',
          'Elixir.Phoenix.HTML.Safe.Phoenix.LiveView.JS',
          'Elixir.Phoenix.HTML.Safe.Phoenix.LiveView.Rendered',
          'Elixir.Phoenix.LiveComponent','Elixir.Phoenix.LiveComponent.CID',
          'Elixir.Phoenix.LiveView','Elixir.Phoenix.LiveView.Application',
          'Elixir.Phoenix.LiveView.Async',
          'Elixir.Phoenix.LiveView.AsyncResult',
          'Elixir.Phoenix.LiveView.Channel',
          'Elixir.Phoenix.LiveView.ColocatedHook',
          'Elixir.Phoenix.LiveView.ColocatedJS',
          'Elixir.Phoenix.LiveView.Component',
          'Elixir.Phoenix.LiveView.Comprehension',
          'Elixir.Phoenix.LiveView.Controller',
          'Elixir.Phoenix.LiveView.Debug','Elixir.Phoenix.LiveView.Diff',
          'Elixir.Phoenix.LiveView.Engine',
          'Elixir.Phoenix.LiveView.HTMLAlgebra',
          'Elixir.Phoenix.LiveView.HTMLEngine',
          'Elixir.Phoenix.LiveView.HTMLFormatter',
          'Elixir.Phoenix.LiveView.Helpers','Elixir.Phoenix.LiveView.JS',
          'Elixir.Phoenix.LiveView.Lifecycle',
          'Elixir.Phoenix.LiveView.LiveStream',
          'Elixir.Phoenix.LiveView.Logger','Elixir.Phoenix.LiveView.Plug',
          'Elixir.Phoenix.LiveView.ReloadError',
          'Elixir.Phoenix.LiveView.Rendered',
          'Elixir.Phoenix.LiveView.Renderer','Elixir.Phoenix.LiveView.Route',
          'Elixir.Phoenix.LiveView.Router','Elixir.Phoenix.LiveView.Session',
          'Elixir.Phoenix.LiveView.Socket',
          'Elixir.Phoenix.LiveView.Socket.AssignsNotInSocket',
          'Elixir.Phoenix.LiveView.Static',
          'Elixir.Phoenix.LiveView.TagEngine',
          'Elixir.Phoenix.LiveView.Tokenizer',
          'Elixir.Phoenix.LiveView.Tokenizer.ParseError',
          'Elixir.Phoenix.LiveView.Upload',
          'Elixir.Phoenix.LiveView.UploadChannel',
          'Elixir.Phoenix.LiveView.UploadConfig',
          'Elixir.Phoenix.LiveView.UploadEntry',
          'Elixir.Phoenix.LiveView.UploadTmpFileWriter',
          'Elixir.Phoenix.LiveView.UploadWriter',
          'Elixir.Phoenix.LiveView.Utils','Elixir.Phoenix.LiveViewTest',
          'Elixir.Phoenix.LiveViewTest.ClientProxy',
          'Elixir.Phoenix.LiveViewTest.DOM',
          'Elixir.Phoenix.LiveViewTest.Diff',
          'Elixir.Phoenix.LiveViewTest.Element',
          'Elixir.Phoenix.LiveViewTest.TreeDOM',
          'Elixir.Phoenix.LiveViewTest.Upload',
          'Elixir.Phoenix.LiveViewTest.UploadClient',
          'Elixir.Phoenix.LiveViewTest.View',
          'Elixir.String.Chars.Phoenix.LiveComponent.CID']},
     {compile_env,
         [{phoenix_live_view,[enable_expensive_runtime_checks],{ok,true}}]},
     {optional_applications,[igniter,phoenix_view,jason,lazy_html]},
     {applications,
         [kernel,stdlib,elixir,logger,igniter,phoenix,plug,phoenix_template,
          phoenix_html,telemetry,phoenix_view,jason,lazy_html]},
     {description,
         "Rich, real-time user experiences with server-rendered HTML\n"},
     {registered,[]},
     {vsn,"1.1.8"},
     {mod,{'Elixir.Phoenix.LiveView.Application',[]}}]}.
