{application,toml,
             [{modules,['Elixir.Toml','Elixir.Toml.Builder',
                        'Elixir.Toml.Decoder','Elixir.Toml.Document',
                        'Elixir.Toml.Error','Elixir.Toml.Lexer',
                        'Elixir.Toml.Lexer.Guards','Elixir.Toml.Lexer.String',
                        'Elixir.Toml.Provider','Elixir.Toml.Transform']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"An implementation of TOML for Elixir projects"},
              {registered,[]},
              {vsn,"0.7.0"}]}.
