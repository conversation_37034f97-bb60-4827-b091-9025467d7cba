{application,phoenix_pubsub,
             [{modules,['Elixir.Phoenix.PubSub',
                        'Elixir.Phoenix.PubSub.Adapter',
                        'Elixir.Phoenix.PubSub.Application',
                        'Elixir.Phoenix.PubSub.BroadcastError',
                        'Elixir.Phoenix.PubSub.PG2',
                        'Elixir.Phoenix.PubSub.PG2Worker',
                        'Elixir.Phoenix.PubSub.Supervisor',
                        'Elixir.Phoenix.Tracker',
                        'Elixir.Phoenix.Tracker.Clock',
                        'Elixir.Phoenix.Tracker.DeltaGeneration',
                        'Elixir.Phoenix.Tracker.Replica',
                        'Elixir.Phoenix.Tracker.Shard',
                        'Elixir.Phoenix.Tracker.State']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,crypto]},
              {description,"Distributed PubSub and Presence platform"},
              {registered,[]},
              {vsn,"2.1.3"},
              {mod,{'Elixir.Phoenix.PubSub.Application',[]}}]}.
