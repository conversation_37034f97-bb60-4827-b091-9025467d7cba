{application,phoenix_html,
             [{modules,['Elixir.Phoenix.HTML','Elixir.Phoenix.HTML.Engine',
                        'Elixir.Phoenix.HTML.Form',
                        'Elixir.Phoenix.HTML.FormData',
                        'Elixir.Phoenix.HTML.FormData.Map',
                        'Elixir.Phoenix.HTML.FormField',
                        'Elixir.Phoenix.HTML.Safe',
                        'Elixir.Phoenix.HTML.Safe.Atom',
                        'Elixir.Phoenix.HTML.Safe.BitString',
                        'Elixir.Phoenix.HTML.Safe.Date',
                        'Elixir.Phoenix.HTML.Safe.DateTime',
                        'Elixir.Phoenix.HTML.Safe.Float',
                        'Elixir.Phoenix.HTML.Safe.Integer',
                        'Elixir.Phoenix.HTML.Safe.List',
                        'Elixir.Phoenix.HTML.Safe.NaiveDateTime',
                        'Elixir.Phoenix.HTML.Safe.Time',
                        'Elixir.Phoenix.HTML.Safe.Tuple',
                        'Elixir.Phoenix.HTML.Safe.URI']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,eex,logger]},
              {description,"Phoenix view functions for working with HTML templates"},
              {registered,[]},
              {vsn,"4.2.1"}]}.
