{erl_opts,[debug_info]}.
{deps,[]}.
{profiles,[{test,[{erl_opts,[nowarn_export_all]},
                  {ct_opts,[{ct_hooks,[cth_surefire]}]},
                  {cover_enabled,true},
                  {cover_opts,[verbose]},
                  {plugins,[covertool]},
                  {covertool,[{coverdata_files,["ct.coverdata"]}]}]}]}.
{shell,[{apps,[telemetry]}]}.
{xref_checks,[undefined_function_calls,undefined_functions,locals_not_used,
              deprecated_function_calls,deprecated_functions]}.
{hex,[{doc,#{provider => ex_doc}}]}.
{ex_doc,[{source_url,<<"https://github.com/beam-telemetry/telemetry">>},
         {extras,[<<"README.md">>,<<"CHANGELOG.md">>,<<"LICENSE">>,
                  <<"NOTICE">>]},
         {main,<<"readme">>}]}.
{overrides,[]}.
